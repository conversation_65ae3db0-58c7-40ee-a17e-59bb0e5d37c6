const src = './src'
const build = './build'
const dist = './dist'

module.exports = {
  browsersync: {
    development: {
      browser: ['google chrome'], // 'firefox' 可以设置多个浏览器打开
      server: {
        baseDir: [build],
        directory: true,
        // index:'03-1-1-国内机票预订-搜索结果-单程.html'
      },
      port: 11000,
    },
  },
  autoprefixer: {
    browsers: [
      'last 2 versions',
      // 浏览器前缀得看gulp-less插件,暂时配置last 2 versions
      // 'safari 12',
      // 'ie 11',
      // 'ios 10',
      // 'android 9'
    ],
    cascade: true,
  },
  src: {
    url: src,
    less: {
      main: [
        src + '/less/common.less',
        src + '/pages/home/<USER>',
        src + '/pages/sibo/sibo.less',
        src + '/pages/flightOptions/flightOptions.less', // 航班选择
      ],
      files: [
        src + '/less/**/*.less', // 主less
        src + '/modules/**/*.less', // 业务组件less
        src + '/components/**/*.less', // UI组件less
        src + '/plugins/**/*.less', // 自定义插件less
      ],
    },
    ejs: {
      files: src + '/**/**/*.html',
    },
    data: {
      files: src + '/data',
    },
    modules: {
      tpl: src + '/modules/**/*.ejs',
    },
    js: {
      allFiles: [
        src + '/js/**/*.js',
        src + '/modules/**/*.js',
        src + '/components/**/*.js',
        src + '/pages/**/*.js',
      ],
      common: [
        src + '/js/**/*.js', // 主js
        src + '/modules/**/*.js', // 业务组件js
        src + '/components/**/*.js', // UI组件js
      ],
      pageHome: [
        src + '/pages/home/<USER>/*.js', // 首页js
      ],
    },
    plugins: {
      // files: src + '/plugins/**/*'
      files: [src + '/plugins/**/*.js', src + '/plugins/**/*.css'],
    },
    icon: {
      files: src + '/fonts/**/*',
    },
    img: {
      url: src + '/images',
      files: src + '/images/**/*',
    },
    config: {
      files: src + '/config/**/*.js',
    },
  },
  build: {
    url: build,
    css: {
      url: build + '/css',
      files: build + '/css/**/*.css',
    },
    js: {
      url: build + '/js',
      files: build + '/js/**/*',
    },
    plugins: {
      url: build + '/plugins',
      files: build + '/plugins/**/*',
    },
    img: {
      url: build + '/images',
      files: build + '/images/**/*',
    },
    icon: {
      url: build + '/css/fonts',
      files: build + '/css/fonts/**/*',
    },
  },
  dist: {
    url: dist,
    css: {
      url: dist + '/css',
    },
    js: {
      url: dist + '/js',
    },
    plugins: {
      url: dist + '/plugins',
    },
    icon: {
      url: dist + '/css/fonts',
    },
    img: {
      url: dist + '/images',
    },
  },
}
